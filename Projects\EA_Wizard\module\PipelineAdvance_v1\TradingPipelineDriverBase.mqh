//+------------------------------------------------------------------+
//|                                    TradingPipelineDriverBase.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 確保 TradingMessageHandler 類型可用
#include "TradingErrorHandler.mqh"

// 明確聲明 TradingMessageHandler 類型
#ifndef _TRADING_MESSAGE_HANDLER_DECLARED_
#define _TRADING_MESSAGE_HANDLER_DECLARED_
// TradingMessageHandler 已在 TradingErrorHandler.mqh 中定義
#endif
#include "TradingEvent.mqh"
#include "TradingPipeline.mqh"
#include "interface/ITradingPipelineDriver.mqh"
#include "TradingPipelineContainerManager.mqh"
#include "TradingPipelineRegistry.mqh"
#include "TradingPipelineExplorer.mqh"
#include "ObjectRegistry.mqh"

//+------------------------------------------------------------------+
//| 交易流水線驅動器抽象基類                                         |
//| 提供驅動器管理的基本功能和模板方法模式                           |
//| 遵循 SOLID 原則和模組化設計                                      |
//+------------------------------------------------------------------+
class TradingPipelineDriverBase : public ITradingPipelineDriver
{
protected:
    // 核心組件
    TradingPipelineContainerManager* m_manager;         // 容器管理器
    TradingPipelineRegistry* m_registry;                // 註冊器
    TradingPipelineExplorer* m_explorer;                // 探索器
    ObjectRegistry* m_objectRegistry;                   // 對象註冊器
    TradingMessageHandler* m_errorHandler;              // 訊息處理器

    // 狀態管理
    bool m_isInitialized;                               // 是否已初始化
    string m_name;                                      // 驅動器名稱
    string m_type;                                      // 驅動器類型
    PipelineResult* m_last_result;                      // 執行結果

public:
    // 構造函數
    TradingPipelineDriverBase(string name = "TradingPipelineDriverBase",
                             string type = "PipelineDriverBase")
        : m_manager(NULL),
          m_registry(NULL),
          m_explorer(NULL),
          m_objectRegistry(NULL),
          m_errorHandler(NULL),
          m_isInitialized(false),
          m_name(name),
          m_type(type),
          m_last_result(new PipelineResult(false, "驅動器尚未初始化", name, ERROR_LEVEL_INFO))
    {
    }

    // 析構函數
    virtual ~TradingPipelineDriverBase()
    {
        Cleanup();
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
        if(m_errorHandler != NULL)
        {
            delete m_errorHandler;
            m_errorHandler = NULL;
        }
    }

    // 實現 ITradingPipelineDriver 介面方法

    // 初始化所有組件 - 模板方法
    virtual bool Initialize() override
    {
        // 1. 前置檢查
        if(!PreInitializeCheck())
        {
            SetResult(false, "前置檢查失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 2. 初始化核心組件 - 子類實現
        if(!InitializeComponents())
        {
            Cleanup();
            SetResult(false, "組件初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 3. 設置配置 - 子類實現
        if(!SetupConfiguration())
        {
            Cleanup();
            SetResult(false, "配置設置失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 4. 後置處理
        if(!PostInitializeProcess())
        {
            Cleanup();
            SetResult(false, "後置處理失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        m_isInitialized = true;
        SetResult(true, "驅動器初始化成功", ERROR_LEVEL_INFO);
        return true;
    }

    // 獲取容器管理器
    virtual TradingPipelineContainerManager* GetManager() const override
    {
        return m_manager;
    }

    // 獲取註冊器
    virtual TradingPipelineRegistry* GetRegistry() const override
    {
        return m_registry;
    }

    // 獲取探索器
    virtual TradingPipelineExplorer* GetExplorer() const override
    {
        return m_explorer;
    }

    // 獲取對象註冊器
    virtual ObjectRegistry* GetObjectRegistry() const override
    {
        return m_objectRegistry;
    }

    // 獲取訊息處理器
    virtual TradingMessageHandler* GetErrorHandler() const override
    {
        return m_errorHandler;
    }

    // 檢查初始化狀態
    virtual bool IsInitialized() const override
    {
        return m_isInitialized &&
               m_manager != NULL &&
               m_registry != NULL &&
               m_explorer != NULL &&
               m_objectRegistry != NULL &&
               m_errorHandler != NULL;
    }

    // 獲取驅動器名稱
    virtual string GetName() const override
    {
        return m_name;
    }

    // 獲取驅動器類型
    virtual string GetType() const override
    {
        return m_type;
    }

    // 設置默認配置 - 委託給子類實現
    virtual bool SetupDefaultConfiguration() override
    {
        return SetupConfiguration();
    }

    // 清理所有組件
    virtual void Cleanup() override
    {
        // 按相反順序清理組件
        if(m_explorer != NULL)
        {
            delete m_explorer;
            m_explorer = NULL;
        }

        if(m_objectRegistry != NULL)
        {
            delete m_objectRegistry;
            m_objectRegistry = NULL;
        }

        if(m_registry != NULL)
        {
            delete m_registry;
            m_registry = NULL;
        }

        if(m_manager != NULL)
        {
            delete m_manager;
            m_manager = NULL;
        }

        m_isInitialized = false;
        // 不在 Cleanup 中設置結果，避免覆蓋錯誤狀態
    }

    // 業務方法

    // 獲取執行結果
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }

    // 通知錯誤處理器當前結果
    void NotifyErrorHandler() const
    {
        if(m_errorHandler != NULL && m_last_result != NULL)
        {
            m_errorHandler.HandlePipelineResult(m_last_result);
        }
    }

    // 獲取結果並通知錯誤處理器 - 便利方法
    PipelineResult* GetResultAndNotify() const
    {
        NotifyErrorHandler();
        return GetResult();
    }

    // 狀態報告
    string GetStatusReport() const
    {
        string report = "=== " + m_type + " 狀態報告 ===\n";
        report += "驅動器名稱: " + m_name + "\n";
        report += "驅動器類型: " + m_type + "\n";
        report += "初始化狀態: " + (m_isInitialized ? "已初始化" : "未初始化") + "\n";
        report += "容器管理器: " + (m_manager != NULL ? "正常" : "NULL") + "\n";
        report += "註冊器: " + (m_registry != NULL ? "正常" : "NULL") + "\n";
        report += "探索器: " + (m_explorer != NULL ? "正常" : "NULL") + "\n";
        report += "對象註冊器: " + (m_objectRegistry != NULL ? "正常" : "NULL") + "\n";
        report += "錯誤處理器: " + (m_errorHandler != NULL ? "正常" : "NULL") + "\n";

        if(IsInitialized())
        {
            report += "\n=== 組件統計信息 ===\n";
            report += "容器數量: " + IntegerToString(GetTotalContainers()) + "\n";
            report += "註冊數量: " + IntegerToString(GetTotalRegistrations()) + "\n";
            report += "對象數量: " + IntegerToString(GetTotalObjects()) + "\n";
        }

        return report;
    }

    // 組件統計信息
    int GetTotalContainers() const
    {
        return (m_manager != NULL) ? m_manager.GetContainerCount() : 0;
    }

    int GetTotalRegistrations() const
    {
        return (m_registry != NULL) ? m_registry.GetTotalRegistrations() : 0;
    }

    int GetTotalObjects() const
    {
        return (m_objectRegistry != NULL) ? m_objectRegistry.GetCount() : 0;
    }

    // 重置功能
    bool Reset()
    {
        // 先設置為未初始化狀態
        m_isInitialized = false;

        // 清理現有組件
        Cleanup();

        // 設置清理完成狀態
        SetResult(true, "組件清理完成", ERROR_LEVEL_INFO);

        // 重新初始化
        bool initResult = Initialize();

        if(initResult)
        {
            SetResult(true, "驅動器重置成功", ERROR_LEVEL_INFO);
        }
        else
        {
            SetResult(false, "驅動器重置失敗", ERROR_LEVEL_ERROR);
        }

        return initResult;
    }

    // 獲取組件詳細信息
    string GetComponentInfo() const
    {
        string info = "=== " + m_type + " 組件信息 ===\n";

        if(m_manager != NULL)
        {
            info += "容器管理器:\n";
            info += "  - 名稱: " + m_manager.GetName() + "\n";
            info += "  - 類型: " + m_manager.GetType() + "\n";
            info += "  - 容器數量: " + IntegerToString(m_manager.GetContainerCount()) + "\n";
            info += "  - 最大容器數: " + IntegerToString(m_manager.GetMaxContainers()) + "\n";
            info += "  - 啟用狀態: " + (m_manager.IsEnabled() ? "啟用" : "禁用") + "\n";
        }

        if(m_registry != NULL)
        {
            info += "\n註冊器:\n";
            info += "  - 名稱: " + m_registry.GetName() + "\n";
            info += "  - 類型: " + m_registry.GetType() + "\n";
            info += "  - 總註冊數: " + IntegerToString(m_registry.GetTotalRegistrations()) + "\n";
            info += "  - 最大註冊數: " + IntegerToString(m_registry.GetMaxRegistrations()) + "\n";
            info += "  - 啟用狀態: " + (m_registry.IsEnabled() ? "啟用" : "禁用") + "\n";
        }

        if(m_explorer != NULL)
        {
            info += "\n探索器:\n";
            info += "  - 名稱: " + m_explorer.GetName() + "\n";
            info += "  - 類型: " + m_explorer.GetType() + "\n";
            info += "  - 描述: " + m_explorer.GetDescription() + "\n";
            info += "  - 有效狀態: " + (m_explorer.IsValid() ? "有效" : "無效") + "\n";
        }

        if(m_objectRegistry != NULL)
        {
            info += "\n對象註冊器:\n";
            info += "  - 名稱: " + m_objectRegistry.GetName() + "\n";
            info += "  - 類型: " + m_objectRegistry.GetType() + "\n";
            info += "  - 對象數量: " + IntegerToString(m_objectRegistry.GetCount()) + "\n";
            info += "  - 擁有權: " + (m_objectRegistry.IsOwned() ? "擁有" : "不擁有") + "\n";
            info += "  - 最後註冊鍵: " + m_objectRegistry.GetLastRegisteredKey() + "\n";
        }

        if(m_errorHandler != NULL)
        {
            info += "\n訊息處理器:\n";
            info += "  - 訊息數量: " + IntegerToString(m_errorHandler.GetMessageCount()) + "\n";
            info += "  - 是否為空: " + (m_errorHandler.IsEmpty() ? "是" : "否") + "\n";

            TradingMessageRecord* lastRecord = m_errorHandler.GetLastMessage();
            if(lastRecord != NULL)
            {
                info += "  - 最後訊息: " + lastRecord.m_message + "\n";
                delete lastRecord;
            }
            else
            {
                info += "  - 最後訊息: 無\n";
            }

            info += "  - 嚴重錯誤: " + (m_errorHandler.HasMessages(ERROR_LEVEL_CRITICAL) ? "有" : "無") + "\n";
        }

        return info;
    }

    // 驗證所有組件狀態
    bool ValidateComponents() const
    {
        bool isValid = true;

        if(m_manager == NULL)
        {
            isValid = false;
        }

        if(m_registry == NULL)
        {
            isValid = false;
        }

        if(m_explorer == NULL)
        {
            isValid = false;
        }

        if(m_objectRegistry == NULL)
        {
            isValid = false;
        }

        if(m_errorHandler == NULL)
        {
            isValid = false;
        }

        if(isValid && m_explorer != NULL && !m_explorer.IsValid())
        {
            isValid = false;
        }

        return isValid;
    }

    // ObjectRegistry 便利方法

    // 註冊對象到 ObjectRegistry
    bool RegisterObject(const string key, void* object, const string name = "",
                       const string description = "", const string type = "")
    {
        if(m_objectRegistry == NULL)
        {
            SetResult(false, "對象註冊器未初始化", ERROR_LEVEL_ERROR);
            return false;
        }

        bool result = m_objectRegistry.Register(key, object, name, description, type);
        if(!result)
        {
            ObjectResult* objResult = m_objectRegistry.GetLastResult();
            if(objResult != NULL)
            {
                SetResult(false, "對象註冊失敗: " + objResult.GetMessage(), ERROR_LEVEL_ERROR);
            }
            else
            {
                SetResult(false, "對象註冊失敗: 未知錯誤", ERROR_LEVEL_ERROR);
            }
        }
        else
        {
            SetResult(true, "對象註冊成功: " + key, ERROR_LEVEL_INFO);
        }

        return result;
    }

    // 從 ObjectRegistry 移除對象
    bool UnregisterObject(const string key)
    {
        if(m_objectRegistry == NULL)
        {
            SetResult(false, "對象註冊器未初始化", ERROR_LEVEL_ERROR);
            return false;
        }

        bool result = m_objectRegistry.Unregister(key);
        if(!result)
        {
            ObjectResult* objResult = m_objectRegistry.GetLastResult();
            if(objResult != NULL)
            {
                SetResult(false, "對象移除失敗: " + objResult.GetMessage(), ERROR_LEVEL_ERROR);
            }
            else
            {
                SetResult(false, "對象移除失敗: 未知錯誤", ERROR_LEVEL_ERROR);
            }
        }
        else
        {
            SetResult(true, "對象移除成功: " + key, ERROR_LEVEL_INFO);
        }

        return result;
    }

    // 從 ObjectRegistry 獲取對象
    void* GetRegisteredObject(const string key)
    {
        if(m_objectRegistry == NULL)
        {
            SetResult(false, "對象註冊器未初始化", ERROR_LEVEL_ERROR);
            return NULL;
        }

        return m_objectRegistry.GetObject(key);
    }

    // 檢查對象是否已註冊
    bool IsObjectRegistered(const string key)
    {
        if(m_objectRegistry == NULL)
        {
            return false;
        }

        return m_objectRegistry.Contains(key);
    }

    // 清空所有註冊的對象
    void ClearRegisteredObjects()
    {
        if(m_objectRegistry != NULL)
        {
            m_objectRegistry.Clear();
            SetResult(true, "所有註冊對象已清空", ERROR_LEVEL_INFO);
        }
    }

protected:
    // 設置執行結果
    void SetResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }
        m_last_result = new PipelineResult(success, message, m_name, errorLevel);
    }

    // 模板方法模式的鉤子方法

    // 前置檢查 - 子類可以覆寫
    virtual bool PreInitializeCheck()
    {
        return true;
    }

    // 初始化核心組件 - 子類必須實現
    virtual bool InitializeComponents() = 0;

    // 設置配置 - 子類必須實現
    virtual bool SetupConfiguration() = 0;

    // 後置處理 - 子類可以覆寫
    virtual bool PostInitializeProcess()
    {
        return true;
    }
};
